const axios = require('axios');

async function testHeyGenEndpoints() {
  const baseUrl = 'http://172.24.175.70:5001';
  
  try {
    console.log('1. Testing token endpoint...');
    const tokenResponse = await axios.post(`${baseUrl}/api/heygen/token`, {});
    console.log('Token response:', tokenResponse.data);
    
    if (!tokenResponse.data.success || !tokenResponse.data.token) {
      throw new Error('Failed to get token');
    }
    
    const token = tokenResponse.data.token;
    console.log('Token obtained successfully');
    
    console.log('\n2. Testing session creation...');
    const sessionResponse = await axios.post(`${baseUrl}/api/heygen/session/new`, {
      token: token,
      avatarID: 'Pedro_Chair_Sitting_public',
      voiceID: ''
    });
    
    console.log('Session response:', sessionResponse.data);
    
  } catch (error) {
    console.error('Error details:');
    console.error('- Message:', error.message);
    console.error('- Response status:', error.response?.status);
    console.error('- Response data:', error.response?.data);
  }
}

testHeyGenEndpoints();
